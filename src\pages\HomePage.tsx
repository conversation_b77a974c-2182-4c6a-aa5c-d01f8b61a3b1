import type React from "react";
import DefaultLayout from "../layout/DefaultLayout";
import { Component, useState } from "react";

// Component imports
import HeroCarousel from "../components/heroCarousel/HeroCarousel";
import PostSlider from "../components/posterSlider/PosterSlider";


const HomePage = () => {
    const [recommendedMovies, setRecommendedMovies] = useState([]);
    const [premierMovies, setPremierMovies] = useState([]);
    const [onlineStreamEvents, setOnlineStreamEvents] = useState([]);
    return (
      <>
        <HeroCarousel />

        <div className="container mx-auto px-4 md:px-12 my-8">
          <PostSlider
            title="Recommended Movies"
            subTitle="List of recommended Movies"
            posters={recommendedMovies}
            isDark={false}
          />
        </div>

        {/* <div className="bg-premier-800 py-12"> */}
        <div className="container mx-auto px-4 md:px-17 my-8 flex flex-col gap-3">
          <div className="hidden md:flex">
            <img
              src="https://assets-in.bmscdn.com/discovery-catalog/collections/tr:w-1440,h-120/stream-leadin-web-collection-202210241242.png"
              className="w-full h-full"
              alt="Rupay image"
            />
          </div>
          <div className="container mx-auto px-4 md:px-12 my-8 bg-background-600">
            <PostSlider
              title="Premiers"
              subTitle="Brand new release every Friday"
              posters={premierMovies}
              isDark={true}
            />
          </div>
          <div className="container mx-auto px-4 md:px-12 my-8">
            <PostSlider
              title="Online Streaming Events"
              subTitle="Online Stream Events"
              posters={onlineStreamEvents}
              isDark={false}
            />
          </div>
        </div>
        {/* </div> */}
      </>
    ); 
}

export default DefaultLayout(HomePage);